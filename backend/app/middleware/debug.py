"""
调试中间件
"""
import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.config import settings


class DebugMiddleware(BaseHTTPMiddleware):
    """调试中间件，用于记录请求和响应信息"""
    
    async def dispatch(self, request: Request, call_next):
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        if settings.DEBUG:
            print(f"\n=== 请求开始 ===")
            print(f"方法: {request.method}")
            print(f"URL: {request.url}")
            print(f"路径: {request.url.path}")
            
            # 记录请求头
            auth_header = request.headers.get("authorization")
            if auth_header:
                print(f"Authorization: {auth_header[:20]}...")
            else:
                print("Authorization: 无")
            
            print(f"User-Agent: {request.headers.get('user-agent', '无')}")
            print(f"Content-Type: {request.headers.get('content-type', '无')}")
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            if settings.DEBUG:
                print(f"状态码: {response.status_code}")
                print(f"处理时间: {process_time:.4f}秒")
                print(f"=== 请求结束 ===\n")
            
            # 添加处理时间到响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 记录异常信息
            process_time = time.time() - start_time
            if settings.DEBUG:
                print(f"请求异常: {str(e)}")
                print(f"处理时间: {process_time:.4f}秒")
                print(f"=== 请求异常结束 ===\n")
            raise
