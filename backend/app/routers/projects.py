"""
项目管理相关路由
"""
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from app.core.security import get_current_active_user
from app.models.user import User
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectResponse
from app.schemas.common import PaginatedResponse

router = APIRouter()


@router.post("/", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    _current_user: User = Depends(get_current_active_user)
):
    """创建项目"""
    project = await Project.create(**project_data.model_dump())
    return ProjectResponse.model_validate(project)


@router.get("/", response_model=PaginatedResponse[ProjectResponse])
async def get_projects(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    _current_user: User = Depends(get_current_active_user)
):
    """获取项目列表"""
    query = Project.all()

    # 搜索过滤
    if search:
        query = query.filter(project_name__icontains=search)

    # 状态过滤
    if status:
        query = query.filter(status=status)

    # 分页
    total = await query.count()
    offset = (page - 1) * size
    projects = await query.offset(offset).limit(size).order_by("-created_at")

    # 计算总页数
    pages = (total + size - 1) // size

    return PaginatedResponse(
        items=[ProjectResponse.model_validate(p) for p in projects],
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: int,
    _current_user: User = Depends(get_current_active_user)
):
    """获取项目详情"""
    project = await Project.get_or_none(id=project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    return ProjectResponse.model_validate(project)


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    _current_user: User = Depends(get_current_active_user)
):
    """更新项目"""
    project = await Project.get_or_none(id=project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )

    # 更新字段
    update_data = project_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(project, field, value)

    await project.save()
    return ProjectResponse.model_validate(project)


@router.delete("/{project_id}")
async def delete_project(
    project_id: int,
    _current_user: User = Depends(get_current_active_user)
):
    """删除项目"""
    project = await Project.get_or_none(id=project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )

    await project.delete()
    return {"message": "项目删除成功"}


@router.get("/{project_id}/stats")
async def get_project_stats(
    project_id: int,
    _current_user: User = Depends(get_current_active_user)
):
    """获取项目统计信息"""
    project = await Project.get_or_none(id=project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )

    from app.models.demand import Demand
    from app.models.test_case import TestCase
    from app.models.defect import Defect

    # 统计项目相关数据
    demand_count = await Demand.filter(project=project).count()
    test_case_count = await TestCase.filter(demand__project=project).count()
    defect_count = await Defect.filter(project=project).count()

    return {
        "project_id": project_id,
        "demand_count": demand_count,
        "test_case_count": test_case_count,
        "defect_count": defect_count
    }
