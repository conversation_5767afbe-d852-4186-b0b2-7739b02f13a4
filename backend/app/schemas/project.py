"""
项目相关模式
"""
from typing import Optional
from datetime import datetime, date
from pydantic import BaseModel
from app.models.project import ProjectStatus


class ProjectBase(BaseModel):
    """项目基础模式"""
    project_name: str
    description: Optional[str] = None
    status: ProjectStatus = ProjectStatus.PLANNING
    start_date: Optional[date] = None
    end_date: Optional[date] = None


class ProjectCreate(ProjectBase):
    """项目创建模式"""
    pass


class ProjectUpdate(BaseModel):
    """项目更新模式"""
    project_name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ProjectStatus] = None

    start_date: Optional[date] = None
    end_date: Optional[date] = None


class ProjectResponse(ProjectBase):
    """项目响应模式"""
    id: int
    owner_id: int

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
