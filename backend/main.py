"""
eror Star AI(紫薇智测)智能平台 - 主应用入口
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from tortoise.contrib.fastapi import register_tortoise

from app.core.config import settings
from app.routers import (
    auth, projects, demands, test_cases,
    interfaces, test_plans, test_executions,
    defects, ai_assistant, reports, dashboard
)

# 创建 FastAPI 应用实例
app = FastAPI(
    title="Emperor Star AI(紫薇智测)智能平台",
    description="基于AI的软件测试用例智能生成与管理平台",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(dashboard.router, prefix="/api/v1/dashboard", tags=["首页仪表板"])
app.include_router(projects.router, prefix="/api/v1/projects", tags=["项目管理"])
app.include_router(demands.router, prefix="/api/v1/demands", tags=["需求管理"])
app.include_router(test_cases.router, prefix="/api/v1/test-cases", tags=["用例管理"])
app.include_router(interfaces.router, prefix="/api/v1/interfaces", tags=["接口管理"])
app.include_router(test_plans.router, prefix="/api/v1/test-plans", tags=["测试计划"])
app.include_router(test_executions.router, prefix="/api/v1/test-executions", tags=["测试执行"])
app.include_router(defects.router, prefix="/api/v1/defects", tags=["缺陷管理"])
app.include_router(ai_assistant.router, prefix="/api/v1/ai", tags=["AI辅助"])
app.include_router(reports.router, prefix="/api/v1/reports", tags=["报表统计"])

# 注册数据库
register_tortoise(
    app,
    db_url=settings.DATABASE_URL,
    modules={"models": ["app.models"]},
    generate_schemas=True,
    add_exception_handlers=True,
)

@app.get("/")
async def root():
    """根路径"""
    return {"message": "软件测试用例智能平台 API"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
