import { request } from './request'
import type { Project, ProjectForm, PaginatedResponse, ProjectStats } from '@/types/project'

// 项目管理相关 API
export const projectApi = {
  // 获取项目列表
  getProjects(params?: {
    page?: number
    size?: number
    search?: string
    status?: string
  }): Promise<PaginatedResponse<Project>> {
    return request.get('/projects/', { params })
  },

  // 获取项目详情
  getProject(id: number): Promise<Project> {
    return request.get(`/projects/${id}`)
  },

  // 创建项目
  createProject(data: ProjectForm): Promise<Project> {
    return request.post('/projects/', data)
  },

  // 更新项目
  updateProject(id: number, data: Partial<ProjectForm>): Promise<Project> {
    return request.put(`/projects/${id}`, data)
  },

  // 删除项目
  deleteProject(id: number): Promise<void> {
    return request.delete(`/projects/${id}`)
  },

  // 获取项目统计信息
  getProjectStats(id: number): Promise<ProjectStats> {
    return request.get(`/projects/${id}/stats`)
  },
}
