<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <AppHeader />

    <div class="app-container">
      <!-- 侧边栏 -->
      <AppSidebar />

      <!-- 主内容区域 -->
      <div class="app-main" :class="{ 'sidebar-collapsed': appStore.sidebarCollapsed }">
        <!-- 标签页导航 -->
        <AppTabs />

        <!-- 页面内容 -->
        <div class="app-content">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { useTabsStore } from '@/stores/tabs'
import AppHeader from './AppHeader.vue'
import AppTabs from './AppTabs.vue'
import AppSidebar from './AppSidebar.vue'

const appStore = useAppStore()
const userStore = useUserStore()
const tabsStore = useTabsStore()

onMounted(() => {
  // 初始化应用状态
  appStore.initializeApp()

  // 初始化标签页
  tabsStore.initTabs()

  // 检查令牌有效性
  if (userStore.token) {
    userStore.checkTokenValidity()
  }
})
</script>

<style lang="scss" scoped>
.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: $sidebar-width;
  transition: $transition-base;
  min-height: 0; // 确保 flex 子元素可以正确收缩

  &.sidebar-collapsed {
    margin-left: $sidebar-collapsed-width;
  }
}

.app-content {
  flex: 1;
  padding: $spacing-lg;
  overflow-y: auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
  border-top: 1px solid rgba(0, 212, 255, 0.1);

  // 科技感网格背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
  }

  // 确保内容在网格之上
  > * {
    position: relative;
    z-index: 1;
  }

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.3);
    border-radius: 4px;

    &:hover {
      background: rgba(0, 212, 255, 0.5);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-main {
    margin-left: 0;

    &.sidebar-collapsed {
      margin-left: 0;
    }
  }

  .app-content {
    padding: $spacing-md;
  }
}
</style>
