import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useTabsStore } from '@/stores/tabs'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard',
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false },
    },
    {
      path: '/',
      component: () => import('@/components/layout/AppLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/DashboardView.vue'),
        },
        {
          path: 'projects',
          name: 'Projects',
          component: () => import('@/views/ProjectsView.vue'),
        },
        {
          path: 'demands',
          children: [
            {
              path: 'ai-analysis',
              name: 'DemandAIAnalysis',
              component: () => import('@/views/demands/AIAnalysisView.vue'),
            },
            {
              path: 'list',
              name: 'DemandList',
              component: () => import('@/views/demands/ListView.vue'),
            },
          ],
        },
        {
          path: 'test-cases',
          children: [
            {
              path: 'ai-generate',
              name: 'TestCaseAIGenerate',
              component: () => import('@/views/test-cases/AIGenerateView.vue'),
            },
            {
              path: 'list',
              name: 'TestCaseList',
              component: () => import('@/views/test-cases/ListView.vue'),
            },
          ],
        },
        {
          path: 'interfaces',
          children: [
            {
              path: 'ai-test',
              name: 'InterfaceAITest',
              component: () => import('@/views/interfaces/AITestView.vue'),
            },
            {
              path: 'list',
              name: 'InterfaceList',
              component: () => import('@/views/interfaces/ListView.vue'),
            },
          ],
        },
        {
          path: 'test-plans',
          name: 'TestPlans',
          component: () => import('@/views/TestPlansView.vue'),
        },
        {
          path: 'test-executions',
          name: 'TestExecutions',
          component: () => import('@/views/TestExecutionsView.vue'),
        },
        {
          path: 'defects',
          name: 'Defects',
          component: () => import('@/views/DefectsView.vue'),
        },
        {
          path: 'ai',
          children: [
            {
              path: 'recommend-cases',
              name: 'AIRecommendCases',
              component: () => import('@/views/ai/RecommendCasesView.vue'),
            },
            {
              path: 'risk-prediction',
              name: 'AIRiskPrediction',
              component: () => import('@/views/ai/RiskPredictionView.vue'),
            },
          ],
        },
        {
          path: 'reports',
          name: 'Reports',
          component: () => import('@/views/ReportsView.vue'),
        },
        {
          path: 'profile',
          name: 'Profile',
          component: () => import('@/views/ProfileView.vue'),
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/views/SettingsView.vue'),
        },
        {
          path: 'auth-test',
          name: 'AuthTest',
          component: () => import('@/views/AuthTestView.vue'),
        },
      ],
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFoundView.vue'),
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const tabsStore = useTabsStore()
  const requiresAuth = to.meta.requiresAuth !== false

  // 如果有 token 但没有用户信息，尝试获取用户信息
  if (userStore.token && !userStore.userInfo) {
    try {
      await userStore.fetchUserInfo()
    } catch (error) {
      // 令牌无效，清除状态
      console.error('Token validation failed:', error)
      await userStore.logout()
      if (requiresAuth) {
        next('/login')
        return
      }
    }
  }

  if (requiresAuth && !userStore.isLoggedIn) {
    // 需要认证但未登录，重定向到登录页
    next('/login')
  } else if (to.path === '/login' && userStore.isLoggedIn) {
    // 已登录用户访问登录页，重定向到首页
    next('/dashboard')
  } else {
    next()
  }
})

// 路由后置守卫 - 添加标签页
router.afterEach((to) => {
  const tabsStore = useTabsStore()

  // 只为需要认证的页面添加标签页
  if (to.meta.requiresAuth !== false && to.path !== '/login') {
    tabsStore.addTab(to.path)
    // 保存到本地存储
    tabsStore.saveTabs()
  }
})

export default router
