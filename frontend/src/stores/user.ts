import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'
import type { UserInfo, LoginForm, LoginResponse } from '@/types/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)

  // 从本地存储恢复状态
  const initializeStore = () => {
    console.log('Initializing user store...')
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')

    console.log('Saved token:', savedToken ? savedToken.substring(0, 50) + '...' : 'none')
    console.log('Saved user info:', savedUserInfo ? 'exists' : 'none')

    if (savedToken) {
      token.value = savedToken
      console.log('Token restored to store')
    }

    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
        console.log('User info restored to store:', userInfo.value?.username)
      } catch (error) {
        console.error('Failed to parse user info from localStorage:', error)
        localStorage.removeItem('userInfo')
      }
    }

    console.log('Store initialization complete. isLoggedIn:', isLoggedIn.value)
  }

  // 登录
  const login = async (loginForm: LoginForm): Promise<LoginResponse> => {
    try {
      const response = await authApi.login(loginForm)

      // 保存令牌和用户信息
      token.value = response.access_token
      userInfo.value = response.user

      // 持久化到本地存储
      localStorage.setItem('token', response.access_token)
      localStorage.setItem('userInfo', JSON.stringify(response.user))

      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout API failed:', error)
    } finally {
      // 清除状态和本地存储
      token.value = ''
      userInfo.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }

  // 获取当前用户信息
  const fetchUserInfo = async (): Promise<UserInfo> => {
    try {
      const user = await authApi.getCurrentUser()
      userInfo.value = user
      localStorage.setItem('userInfo', JSON.stringify(user))
      return user
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      throw error
    }
  }

  // 更新用户信息
  const updateUserInfo = async (data: Partial<UserInfo>): Promise<UserInfo> => {
    try {
      const updatedUser = await authApi.updateProfile(data)
      userInfo.value = updatedUser
      localStorage.setItem('userInfo', JSON.stringify(updatedUser))
      return updatedUser
    } catch (error) {
      console.error('Failed to update user info:', error)
      throw error
    }
  }

  // 检查令牌有效性
  const checkTokenValidity = async (): Promise<boolean> => {
    if (!token.value) {
      return false
    }

    try {
      await fetchUserInfo()
      return true
    } catch (error) {
      // 令牌无效，清除状态
      logout()
      return false
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,

    // 方法
    initializeStore,
    login,
    logout,
    fetchUserInfo,
    updateUserInfo,
    checkTokenValidity,
  }
})
