import type { PaginatedResponse } from './common'

// 项目状态枚举
export enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

// 项目接口
export interface Project {
  id: number
  project_name: string
  description?: string
  status: ProjectStatus
  start_date?: string
  end_date?: string
  created_at: string
  updated_at: string
}

// 项目表单接口
export interface ProjectForm {
  project_name: string
  description?: string
  status?: ProjectStatus
  start_date?: string | null
  end_date?: string | null
}

// 项目统计接口
export interface ProjectStats {
  project_id: number
  demand_count: number
  test_case_count: number
  defect_count: number
}

// 导出类型
export type { PaginatedResponse }
