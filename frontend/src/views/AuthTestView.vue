<template>
  <div class="auth-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>认证测试工具</span>
        </div>
      </template>

      <div class="test-section">
        <h3>当前状态</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="登录状态">
            <el-tag :type="userStore.isLoggedIn ? 'success' : 'danger'">
              {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Token">
            <el-text class="token-text" truncated>
              {{ userStore.token || '无' }}
            </el-text>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ userStore.userInfo?.username || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            {{ userStore.userInfo?.role || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h3>API 测试</h3>
        <el-space direction="vertical" style="width: 100%">
          <el-button 
            type="primary" 
            @click="testLogin"
            :loading="loading.login"
          >
            测试登录
          </el-button>
          
          <el-button 
            type="info" 
            @click="testGetUserInfo"
            :loading="loading.userInfo"
            :disabled="!userStore.token"
          >
            测试获取用户信息
          </el-button>
          
          <el-button
            type="success"
            @click="testDashboard"
            :loading="loading.dashboard"
            :disabled="!userStore.token"
          >
            测试仪表板接口
          </el-button>

          <el-button
            type="success"
            @click="testProjects"
            :loading="loading.projects"
            :disabled="!userStore.token"
          >
            测试项目接口
          </el-button>
          
          <el-button 
            type="warning" 
            @click="testRefreshToken"
            :loading="loading.refresh"
            :disabled="!userStore.token"
          >
            测试刷新Token
          </el-button>
          
          <el-button 
            type="danger" 
            @click="testLogout"
            :loading="loading.logout"
            :disabled="!userStore.token"
          >
            测试登出
          </el-button>
        </el-space>
      </div>

      <div class="test-section">
        <h3>测试结果</h3>
        <el-scrollbar height="300px">
          <div class="test-results">
            <div 
              v-for="(result, index) in testResults" 
              :key="index"
              class="test-result-item"
              :class="result.type"
            >
              <div class="result-header">
                <span class="result-time">{{ result.time }}</span>
                <span class="result-action">{{ result.action }}</span>
                <el-tag :type="result.type === 'success' ? 'success' : 'danger'" size="small">
                  {{ result.type === 'success' ? '成功' : '失败' }}
                </el-tag>
              </div>
              <div class="result-content">{{ result.message }}</div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import { request } from '@/api/request'

const userStore = useUserStore()

const loading = reactive({
  login: false,
  userInfo: false,
  dashboard: false,
  projects: false,
  refresh: false,
  logout: false
})

interface TestResult {
  time: string
  action: string
  type: 'success' | 'error'
  message: string
}

const testResults = ref<TestResult[]>([])

const addTestResult = (action: string, type: 'success' | 'error', message: string) => {
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action,
    type,
    message
  })
}

const testLogin = async () => {
  loading.login = true
  try {
    const result = await userStore.login({
      username: 'admin',
      password: '123456'
    })
    addTestResult('登录', 'success', `登录成功，获得token: ${result.access_token.substring(0, 50)}...`)
    ElMessage.success('登录测试成功')
  } catch (error: any) {
    addTestResult('登录', 'error', `登录失败: ${error.message || error}`)
    ElMessage.error('登录测试失败')
  } finally {
    loading.login = false
  }
}

const testGetUserInfo = async () => {
  loading.userInfo = true
  try {
    const userInfo = await authApi.getCurrentUser()
    addTestResult('获取用户信息', 'success', `成功获取用户信息: ${userInfo.username} (${userInfo.role})`)
    ElMessage.success('获取用户信息测试成功')
  } catch (error: any) {
    addTestResult('获取用户信息', 'error', `获取用户信息失败: ${error.message || error}`)
    ElMessage.error('获取用户信息测试失败')
  } finally {
    loading.userInfo = false
  }
}

const testDashboard = async () => {
  loading.dashboard = true
  try {
    const data = await request.get('/dashboard/stats')
    addTestResult('仪表板接口', 'success', `成功获取仪表板数据，项目数: ${data.overview.project_count}`)
    ElMessage.success('仪表板接口测试成功')
  } catch (error: any) {
    addTestResult('仪表板接口', 'error', `仪表板接口失败: ${error.message || error}`)
    ElMessage.error('仪表板接口测试失败')
  } finally {
    loading.dashboard = false
  }
}

const testProjects = async () => {
  loading.projects = true
  try {
    // 检查localStorage中的token
    const localToken = localStorage.getItem('token')
    addTestResult('Token检查', 'success', `localStorage中的token: ${localToken ? localToken.substring(0, 50) + '...' : '无'}`)

    // 检查store中的token
    const storeToken = userStore.token
    addTestResult('Store Token检查', 'success', `Store中的token: ${storeToken ? storeToken.substring(0, 50) + '...' : '无'}`)

    const data = await request.get('/projects')
    addTestResult('项目接口', 'success', `成功获取项目数据，项目数: ${data.items.length}`)
    ElMessage.success('项目接口测试成功')
  } catch (error: any) {
    addTestResult('项目接口', 'error', `项目接口失败: ${error.message || error}`)
    ElMessage.error('项目接口测试失败')
  } finally {
    loading.projects = false
  }
}

const testRefreshToken = async () => {
  loading.refresh = true
  try {
    const result = await authApi.refreshToken()
    addTestResult('刷新Token', 'success', `Token刷新成功: ${result.access_token.substring(0, 50)}...`)
    ElMessage.success('刷新Token测试成功')
  } catch (error: any) {
    addTestResult('刷新Token', 'error', `刷新Token失败: ${error.message || error}`)
    ElMessage.error('刷新Token测试失败')
  } finally {
    loading.refresh = false
  }
}

const testLogout = async () => {
  loading.logout = true
  try {
    await userStore.logout()
    addTestResult('登出', 'success', '登出成功')
    ElMessage.success('登出测试成功')
  } catch (error: any) {
    addTestResult('登出', 'error', `登出失败: ${error.message || error}`)
    ElMessage.error('登出测试失败')
  } finally {
    loading.logout = false
  }
}
</script>

<style lang="scss" scoped>
.auth-test-container {
  padding: 20px;
}

.test-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;

  h3 {
    margin-bottom: 15px;
    color: #303133;
  }
}

.token-text {
  max-width: 200px;
}

.test-results {
  .test-result-item {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    border-left: 4px solid;

    &.success {
      background-color: #f0f9ff;
      border-left-color: #67c23a;
    }

    &.error {
      background-color: #fef0f0;
      border-left-color: #f56c6c;
    }

    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;

      .result-time {
        font-size: 12px;
        color: #909399;
      }

      .result-action {
        font-weight: bold;
      }
    }

    .result-content {
      font-size: 14px;
      color: #606266;
      word-break: break-all;
    }
  }
}
</style>
