<template>
  <div class="projects-view">
    <div class="page-header">
      <h2>项目管理</h2>
      <el-button type="primary" @click="handleCreate">
        <el-icon><Plus /></el-icon>
        新建项目
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目名称">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入项目名称"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="项目状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            @change="handleSearch"
            style="width: 240px"
          >
            <el-option label="计划中" value="planning" />
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 项目列表 -->
    <div class="projects-grid">
      <div
        v-for="project in projects"
        :key="project.id"
        class="project-card"
        @click="handleProjectClick(project)"
      >
        <div class="project-header">
          <h3 class="project-name">{{ project.project_name }}</h3>
          <el-tag :type="getStatusType(project.status)">
            {{ getStatusText(project.status) }}
          </el-tag>
        </div>

        <p class="project-description">
          {{ project.description || '暂无描述' }}
        </p>

        <div class="project-meta">
          <div class="meta-item">
            <el-icon><Calendar /></el-icon>
            <span>创建时间: {{ formatDate(project.created_at) }}</span>
          </div>
        </div>

        <div class="project-actions">
          <el-button link @click.stop="handleEdit(project)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button link type="danger" @click.stop="handleDelete(project)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑项目对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingProject ? '编辑项目' : '新建项目'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="projectFormRef"
        :model="projectForm"
        :rules="projectRules"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="project_name">
          <el-input v-model="projectForm.project_name" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入项目描述"
          />
        </el-form-item>

        <el-form-item label="项目状态" prop="status">
          <el-select v-model="projectForm.status" placeholder="请选择状态">
            <el-option label="计划中" value="planning" />
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>

        <el-form-item label="开始日期" prop="start_date">
          <el-date-picker
            v-model="projectForm.start_date"
            type="date"
            placeholder="请选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="结束日期" prop="end_date">
          <el-date-picker
            v-model="projectForm.end_date"
            type="date"
            placeholder="请选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '提交中...' : '确定' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  User,
  Calendar,
  Edit,
  Delete,
} from '@element-plus/icons-vue'
import { projectApi } from '@/api/projects'
import { useUserStore } from '@/stores/user'
import type { Project, ProjectForm } from '@/types/project'
import { format } from 'date-fns'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showCreateDialog = ref(false)
const editingProject = ref<Project | null>(null)
const projectFormRef = ref<FormInstance>()

const projects = ref<Project[]>([])
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
})

const searchForm = reactive({
  search: '',
  status: '',
})

const projectForm = reactive<ProjectForm>({
  project_name: '',
  description: '',
  status: 'planning',
  start_date: null,
  end_date: null,
})

// 表单验证规则
const projectRules: FormRules = {
  project_name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择项目状态', trigger: 'change' },
  ],
}

// 方法
const fetchProjects = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      search: searchForm.search || undefined,
      status: searchForm.status || undefined,
    }

    const response = await projectApi.getProjects(params)
    projects.value = response.items
    pagination.total = response.total
  } catch (error: any) {
    console.error('Failed to fetch projects:', error)
    ElMessage.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchProjects()
}

const handleReset = () => {
  searchForm.search = ''
  searchForm.status = ''
  handleSearch()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  fetchProjects()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchProjects()
}

const handleProjectClick = (project: Project) => {
  // 跳转到项目详情页
  console.log('Navigate to project detail:', project.id)
}

const handleCreate = () => {
  resetForm()
  showCreateDialog.value = true
}

const handleCancel = () => {
  showCreateDialog.value = false
  resetForm()
}

const handleEdit = (project: Project) => {
  editingProject.value = project
  Object.assign(projectForm, {
    ...project,
    start_date: project.start_date || null,
    end_date: project.end_date || null,
  })
  showCreateDialog.value = true
}

const handleDelete = async (project: Project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目"${project.project_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await projectApi.deleteProject(project.id)
    ElMessage.success('删除成功')
    fetchProjects()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Failed to delete project:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!projectFormRef.value) return

  try {
    const valid = await projectFormRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 准备提交数据，处理日期字段
    const submitData = {
      ...projectForm,
      start_date: projectForm.start_date || undefined,
      end_date: projectForm.end_date || undefined,
    }

    if (editingProject.value) {
      await projectApi.updateProject(editingProject.value.id, submitData)
      ElMessage.success('更新成功')
    } else {
      await projectApi.createProject(submitData)
      ElMessage.success('创建成功')
    }

    showCreateDialog.value = false
    resetForm()
    fetchProjects()
  } catch (error: any) {
    console.error('Failed to submit project:', error)

    // 提取具体的错误信息
    let errorMessage = '提交失败'
    if (error.response?.data?.detail) {
      if (Array.isArray(error.response.data.detail)) {
        errorMessage = error.response.data.detail.map((item: any) => item.msg || item).join(', ')
      } else {
        errorMessage = error.response.data.detail
      }
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  editingProject.value = null
  Object.assign(projectForm, {
    project_name: '',
    description: '',
    status: 'planning',
    start_date: null,
    end_date: null,
  })
  projectFormRef.value?.resetFields()
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    planning: 'warning',
    active: 'primary',
    completed: 'success',
    archived: 'info',
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    planning: '计划中',
    active: '进行中',
    completed: '已完成',
    archived: '已归档',
  }
  return textMap[status] || status
}

const formatDate = (date: string) => {
  try {
    return format(new Date(date), 'yyyy-MM-dd')
  } catch (error) {
    return date
  }
}

// 生命周期
onMounted(() => {
  fetchProjects()
})
</script>

<style lang="scss" scoped>
.projects-view {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h2 {
      margin: 0;
      color: $text-primary;
    }
  }

  .search-card {
    margin-bottom: $spacing-lg;
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: $spacing-lg;
    margin-bottom: $spacing-lg;
  }

  .project-card {
    background: $bg-secondary;
    border-radius: $border-radius-large;
    box-shadow: $box-shadow-light;
    padding: $spacing-lg;
    cursor: pointer;
    transition: $transition-base;

    &:hover {
      box-shadow: $box-shadow-dark;
      transform: translateY(-2px);
    }
  }

  .project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
  }

  .project-name {
    margin: 0;
    font-size: $font-size-medium;
    color: $text-primary;
    font-weight: 600;
  }

  .project-description {
    color: $text-secondary;
    margin-bottom: $spacing-md;
    line-height: 1.5;
  }

  .project-meta {
    margin-bottom: $spacing-md;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    color: $text-secondary;
    font-size: $font-size-small;
    margin-bottom: $spacing-xs;
  }

  .project-actions {
    display: flex;
    gap: $spacing-sm;
    border-top: 1px solid $border-extra-light;
    padding-top: $spacing-md;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: $spacing-lg;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .projects-view {
    .page-header {
      flex-direction: column;
      gap: $spacing-md;
      align-items: stretch;
    }

    .projects-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
