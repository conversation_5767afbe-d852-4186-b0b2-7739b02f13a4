#!/usr/bin/env python3
"""
测试认证流程的脚本
"""
import asyncio
import aiohttp
import json

async def test_auth_flow():
    """测试认证流程"""
    base_url = "http://localhost:8000/api/v1"
    
    async with aiohttp.ClientSession() as session:
        print("=== 测试认证流程 ===")
        
        # 1. 测试登录
        print("\n1. 测试登录...")
        login_data = {
            "username": "admin",
            "password": "123456"
        }
        
        try:
            async with session.post(f"{base_url}/auth/login", json=login_data) as resp:
                print(f"登录响应状态码: {resp.status}")
                if resp.status == 200:
                    login_result = await resp.json()
                    print("登录成功!")
                    print(f"Token: {login_result['access_token'][:50]}...")
                    token = login_result['access_token']
                else:
                    error_text = await resp.text()
                    print(f"登录失败: {error_text}")
                    return
        except Exception as e:
            print(f"登录请求异常: {e}")
            return
        
        # 2. 测试获取用户信息
        print("\n2. 测试获取用户信息...")
        headers = {"Authorization": f"Bearer {token}"}
        
        try:
            async with session.get(f"{base_url}/auth/me", headers=headers) as resp:
                print(f"获取用户信息响应状态码: {resp.status}")
                if resp.status == 200:
                    user_info = await resp.json()
                    print("获取用户信息成功!")
                    print(f"用户名: {user_info['username']}")
                    print(f"角色: {user_info['role']}")
                else:
                    error_text = await resp.text()
                    print(f"获取用户信息失败: {error_text}")
        except Exception as e:
            print(f"获取用户信息请求异常: {e}")
        
        # 3. 测试访问需要认证的接口
        print("\n3. 测试访问仪表板接口...")
        try:
            async with session.get(f"{base_url}/dashboard/stats", headers=headers) as resp:
                print(f"仪表板接口响应状态码: {resp.status}")
                if resp.status == 200:
                    dashboard_data = await resp.json()
                    print("访问仪表板接口成功!")
                    print(f"响应数据: {json.dumps(dashboard_data, indent=2, ensure_ascii=False)}")
                else:
                    error_text = await resp.text()
                    print(f"访问仪表板接口失败: {error_text}")
        except Exception as e:
            print(f"访问仪表板接口请求异常: {e}")
        
        # 4. 测试无效token
        print("\n4. 测试无效token...")
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        try:
            async with session.get(f"{base_url}/auth/me", headers=invalid_headers) as resp:
                print(f"无效token响应状态码: {resp.status}")
                error_text = await resp.text()
                print(f"无效token响应: {error_text}")
        except Exception as e:
            print(f"无效token请求异常: {e}")

if __name__ == "__main__":
    asyncio.run(test_auth_flow())
